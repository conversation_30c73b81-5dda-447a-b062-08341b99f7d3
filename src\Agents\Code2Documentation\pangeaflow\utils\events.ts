// src/Agents/Code2Documentation/pangeaflow/utils/events.ts

import { EventEmitter } from 'events';

// Create a global event emitter
export const eventEmitter = new EventEmitter();

// Event types
export enum EventType {
  PROGRESS = 'progress',
  STATUS = 'status',
  ERROR = 'error',
  COMPLETE = 'complete'
}

// Event interfaces
export interface ProgressEvent {
  component: string;
  progress: number;
  message: string;
}

export interface StatusEvent {
  component: string;
  status: string;
  message: string;
}

export interface ErrorEvent {
  component: string;
  message: string;
  error: Error;
}

export interface CompleteEvent {
  component: string;
  result: any;
}

// Event emission helpers
export function emitProgress(component: string, progress: number, message: string): void {
  eventEmitter.emit(EventType.PROGRESS, { component, progress, message });
}

export function emitGraphStatus(component: string, progress: number, message: string): void {
  emitProgress(component, progress, message);
  eventEmitter.emit(EventType.STATUS, { component, status: 'running', message });
}

export function emitError(component: string, message: string, error?: Error): void {
  eventEmitter.emit(EventType.ERROR, { 
    component, 
    message, 
    error: error || new Error(message) 
  });
}

export function emitComplete(component: string, result: any): void {
  eventEmitter.emit(EventType.COMPLETE, { component, result });
}

// Event subscription helpers
export function onProgress(callback: (event: ProgressEvent) => void): void {
  eventEmitter.on(EventType.PROGRESS, callback);
}

export function onStatus(callback: (event: StatusEvent) => void): void {
  eventEmitter.on(EventType.STATUS, callback);
}

export function onError(callback: (event: ErrorEvent) => void): void {
  eventEmitter.on(EventType.ERROR, callback);
}

export function onComplete(callback: (event: CompleteEvent) => void): void {
  eventEmitter.on(EventType.COMPLETE, callback);
}

// Cleanup helper
export function removeAllListeners(): void {
  eventEmitter.removeAllListeners();
}
