
import { Link, useLocation } from "react-router-dom";
import { useAuth } from "@/hooks/useAuth";
import { useSubscription } from "@/hooks/useSubscription";
import { useMonthlyTutorialUsage } from "@/hooks/useMonthlyTutorialUsage";
import AuthButtons from "../auth/AuthButtons";
import { useTrialStatus } from "@/hooks/useTrialStatus";
import { ChevronDown, BookOpen, FileText, Users, Zap } from "lucide-react";
import { useState } from "react";

const NavBar = () => {
  const { isSignedIn, isAdmin } = useAuth();
  const { subscribed } = useSubscription();
  const {
    monthlyTutorialsCreated,
    maxTutorialsPerMonth,
    loading: usageLoading
  } = useMonthlyTutorialUsage();

  const { trialStatus, loading } = useTrialStatus();
  const location = useLocation();
  const [showCreateDropdown, setShowCreateDropdown] = useState(false);
  const [showBrowseDropdown, setShowBrowseDropdown] = useState(false);

  console.log("NavBar", { isSignedIn, isAdmin, subscribed, monthlyTutorialsCreated, maxTutorialsPerMonth, usageLoading });
  // Show admin link only when signed in AND user is admin
  const showAdminLink = isSignedIn && isAdmin;

  // Show tutorial usage for subscribed users
  const showTutorialUsage = isSignedIn && subscribed && !usageLoading;
  const isUnlimited = maxTutorialsPerMonth === -1;
  const usageText = isUnlimited 
    ? `${monthlyTutorialsCreated} tutorials` 
    : `${monthlyTutorialsCreated}/${maxTutorialsPerMonth}`;

  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-10">
      <div className="container mx-auto px-4 py-3 flex items-center justify-between">
        <div className="flex items-center">
          <Link to="/" className="flex items-center cursor-pointer">
            <i className="fa-solid fa-book-open text-primary text-2xl mr-2"></i>
            <span className="text-xl font-bold text-gray-800">CodeTutor Pro</span>
          </Link>
          <div className="hidden md:flex ml-10 space-x-6">
            {/* Create Dropdown */}
            <div className="relative">
              <button
                onClick={() => setShowCreateDropdown(!showCreateDropdown)}
                onBlur={() => setTimeout(() => setShowCreateDropdown(false), 150)}
                className="flex items-center text-gray-500 hover:text-primary-600 cursor-pointer"
              >
                Create
                <ChevronDown className="ml-1 h-4 w-4" />
              </button>

              {showCreateDropdown && (
                <div className="absolute top-full left-0 mt-1 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-50">
                  <div className="py-2">
                    <Link
                      to="/dashboard"
                      className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50"
                      onClick={() => setShowCreateDropdown(false)}
                    >
                      <FileText className="mr-3 h-4 w-4 text-blue-600" />
                      <div>
                        <div className="font-medium">Documentation</div>
                        <div className="text-xs text-gray-500">Generate technical documentation</div>
                      </div>
                    </Link>
                    <Link
                      to="/dashboard/create-tutor"
                      className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50"
                      onClick={() => setShowCreateDropdown(false)}
                    >
                      <BookOpen className="mr-3 h-4 w-4 text-green-600" />
                      <div>
                        <div className="font-medium">Interactive Tutorial</div>
                        <div className="text-xs text-gray-500">Create learning experiences with exercises</div>
                      </div>
                    </Link>
                  </div>
                </div>
              )}
            </div>

            {/* Browse Dropdown */}
            <div className="relative">
              <button
                onClick={() => setShowBrowseDropdown(!showBrowseDropdown)}
                onBlur={() => setTimeout(() => setShowBrowseDropdown(false), 150)}
                className="flex items-center text-gray-800 hover:text-primary-600 font-medium cursor-pointer"
              >
                Browse
                <ChevronDown className="ml-1 h-4 w-4" />
              </button>

              {showBrowseDropdown && (
                <div className="absolute top-full left-0 mt-1 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-50">
                  <div className="py-2">
                    <Link
                      to="/dashboard/gallery"
                      className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50"
                      onClick={() => setShowBrowseDropdown(false)}
                    >
                      <FileText className="mr-3 h-4 w-4 text-blue-600" />
                      <div>
                        <div className="font-medium">Documentation Gallery</div>
                        <div className="text-xs text-gray-500">Browse technical documentation</div>
                      </div>
                    </Link>
                    <Link
                      to="/dashboard/tutor-gallery"
                      className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50"
                      onClick={() => setShowBrowseDropdown(false)}
                    >
                      <BookOpen className="mr-3 h-4 w-4 text-green-600" />
                      <div>
                        <div className="font-medium">Tutorial Gallery</div>
                        <div className="text-xs text-gray-500">Browse interactive tutorials</div>
                      </div>
                    </Link>
                  </div>
                </div>
              )}
            </div>

            {isSignedIn && (
              <Link to="/dashboard/subscription" className="text-gray-500 hover:text-primary-600 cursor-pointer">
                Subscription
              </Link>
            )}
            {showAdminLink && (
              <Link to="/admin" className="text-gray-500 hover:text-primary-600 cursor-pointer">
                Admin
              </Link>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {!trialStatus.isInTrial && showTutorialUsage && (
            <div className="hidden md:flex items-center text-sm text-gray-600 bg-gray-100 px-3 py-1 rounded-full">
              <i className="fa-solid fa-chart-line text-primary mr-2"></i>
              <span className="font-medium">{usageText}</span>
            </div>
          )}
          <button className="text-gray-500 hover:text-gray-700 cursor-pointer">
            <i className="fa-regular fa-circle-question"></i>
          </button>
          <button className="text-gray-500 hover:text-gray-700 cursor-pointer">
            <i className="fa-regular fa-bell"></i>
          </button>
          <AuthButtons />
        </div>
      </div>
    </header>
  );
};

export default NavBar;
