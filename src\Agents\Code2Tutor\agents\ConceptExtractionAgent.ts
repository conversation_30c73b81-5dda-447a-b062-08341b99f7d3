// src/Agents/Code2Tutor/agents/ConceptExtractionAgent.ts

import { AgentComponent, ExecutionContext, ExecutionResult } from '@/pangeaflow';
import { SharedStore, LearningConcept } from '../types';
import { emitAgentStatus, emitTutorProgress, emitConceptExtracted, emitTutorError } from '../utils/events';
import { callLlm_openrouter } from '../../shared/callLlm_openrouter';
import { buildPrompt } from '../../../pocketflow/utils/buildPrompt';
import { CONCEPT_EXTRACTION_PROMPT } from '../prompts/conceptExtraction';
import yaml from 'js-yaml';

/**
 * ConceptExtractionAgent - Extracts learning concepts from analyzed code
 * 
 * This agent is responsible for:
 * - Analyzing code files to identify key learning concepts
 * - Determining concept difficulty and prerequisites
 * - Organizing concepts for educational progression
 * - Providing practical examples for each concept
 */
export class ConceptExtractionAgent extends AgentComponent {
  private maxRetries = 3;
  private currentRetry = 0;

  constructor(eventBus: any, telemetry: any, maxRetries = 3) {
    super('concept-extraction', eventBus, telemetry, {
      stage: 'concept-extraction',
      progress: 0
    });
    this.maxRetries = maxRetries;
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    return this.withTelemetry('concept-extraction', async () => {
      console.log('🧠 ConceptExtractionAgent: Starting execution');
      emitAgentStatus('ConceptExtractionAgent', 'starting', 0, 'Initializing concept extraction');

      const shared = context.metadata.shared as SharedStore;

      try {
        console.log('🧠 ConceptExtractionAgent: Validating inputs', {
          files: shared.files?.length || 0,
          project_name: shared.project_name,
          target_audience: shared.target_audience
        });

        // Validate inputs
        if (!shared.files || shared.files.length === 0) {
          const error = new Error('No files available for concept extraction');
          console.error('❌ ConceptExtractionAgent: Validation failed:', error.message);
          emitTutorError(error, 'concept-extraction-validation');
          throw error;
        }

        emitAgentStatus('ConceptExtractionAgent', 'processing', 10, 'Preparing code context');
        emitTutorProgress('Concept Extraction', 10, 'Analyzing code for learning concepts');

        // Prepare context for LLM
        console.log('🧠 ConceptExtractionAgent: Preparing code context');
        const context_str = this.prepareCodeContext(shared.files);
        const file_listing = this.createFileListing(shared.files);

        console.log('🧠 ConceptExtractionAgent: Context prepared', {
          contextLength: context_str.length,
          fileCount: shared.files.length
        });

        emitAgentStatus('ConceptExtractionAgent', 'processing', 30, 'Sending request to LLM for concept analysis');

        // Build prompt for concept extraction
        try {
          const prompt = buildPrompt(CONCEPT_EXTRACTION_PROMPT, {
            project_name: shared.project_name || 'Unknown Project',
            context: context_str,
            max_concepts: shared.max_concepts || 8,
            target_audience: shared.target_audience || 'beginner',
            file_listing_for_prompt: file_listing,
            language_instruction: this.getLanguageInstruction(shared.content_language),
            name_lang_hint: this.getNameLanguageHint(shared.content_language),
            desc_lang_hint: this.getDescriptionLanguageHint(shared.content_language)
          });

          console.log('🧠 ConceptExtractionAgent: Prompt built successfully', {
            promptLength: prompt.length
          });

          emitAgentStatus('ConceptExtractionAgent', 'processing', 50, 'Analyzing code with LLM');

          // Call LLM for concept extraction
          console.log('🧠 ConceptExtractionAgent: Calling LLM');
          const response = await callLlm_openrouter({
            tutorial_id: shared.tutorial_id,
            prompt,
            temperature: 0.7,
            model: "google/gemini-2.5-flash-preview-05-20",
            use_cache: shared.use_cache && this.currentRetry === 0,
            user_id: shared.user_id
          });

          console.log('🧠 ConceptExtractionAgent: LLM response received', {
            responseLength: response?.length || 0,
            hasResponse: !!response
          });

          if (!response) {
            const error = new Error('LLM returned empty response');
            console.error('❌ ConceptExtractionAgent: LLM error:', error.message);
            emitTutorError(error, 'llm-response');
            throw error;
          }

          emitAgentStatus('ConceptExtractionAgent', 'processing', 70, 'Processing LLM response');

          // Log the raw response for debugging
          console.log('🧠 ConceptExtractionAgent - Raw LLM response:', response);

          // Parse the YAML response
          console.log('🧠 ConceptExtractionAgent: Parsing concepts from response');
          const concepts = this.parseConceptsFromResponse(response);

          console.log('🧠 ConceptExtractionAgent: Concepts parsed successfully', {
            conceptCount: concepts.length
          });

          emitAgentStatus('ConceptExtractionAgent', 'processing', 85, `Extracted ${concepts.length} learning concepts`);

          // Validate and enhance concepts
          console.log('🧠 ConceptExtractionAgent: Validating and enhancing concepts');
          const validatedConcepts = this.validateAndEnhanceConcepts(concepts, shared.files);

          console.log('🧠 ConceptExtractionAgent: Concepts validated successfully', {
            validatedCount: validatedConcepts.length
          });

          // Update shared store
          shared.concepts = validatedConcepts;

          // Emit individual concept events
          validatedConcepts.forEach((concept: LearningConcept) => {
            emitConceptExtracted(concept);
          });

          emitAgentStatus('ConceptExtractionAgent', 'completed', 100, `Successfully extracted ${validatedConcepts.length} concepts`);
          emitTutorProgress('Concept Extraction', 100, `Identified ${validatedConcepts.length} key learning concepts`);

          this.emit('concepts.extracted', {
            concepts: validatedConcepts,
            count: validatedConcepts.length
          }, context.id);

          return {
            success: true,
            data: {
              concepts: validatedConcepts,
              conceptCount: validatedConcepts.length
            },
            events: [],
            nextActions: ['plan-tutorial'],
            metadata: {
              conceptsExtracted: validatedConcepts.length,
              targetAudience: shared.target_audience,
              primaryLanguage: shared.language
            }
          };

        } catch (promptError) {
          console.error('❌ ConceptExtractionAgent: Prompt/LLM error:', promptError);
          emitTutorError(promptError as Error, 'prompt-llm');
          throw promptError;
        }

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        console.error('💥 ConceptExtractionAgent: Execution failed:', error);

        this.currentRetry++;

        if (this.currentRetry < this.maxRetries) {
          console.log(`🔄 ConceptExtractionAgent: Retrying (${this.currentRetry}/${this.maxRetries})`);
          emitAgentStatus('ConceptExtractionAgent', 'processing', 0, `Retry ${this.currentRetry}/${this.maxRetries}: ${errorMessage}`);

          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 2000 * this.currentRetry));

          // Retry execution
          return this.execute(context);
        }

        console.error(`💥 ConceptExtractionAgent: Failed after ${this.maxRetries} attempts`);
        emitAgentStatus('ConceptExtractionAgent', 'error', 0, `Failed after ${this.maxRetries} attempts: ${errorMessage}`);
        emitTutorError(error instanceof Error ? error : new Error(errorMessage), 'concept-extraction', {
          retries: this.currentRetry,
          maxRetries: this.maxRetries,
          shared: {
            files: shared?.files?.length || 0,
            project_name: shared?.project_name,
            target_audience: shared?.target_audience
          }
        });

        return {
          success: false,
          error: error instanceof Error ? error : new Error(errorMessage),
          events: [],
          nextActions: ['error'],
          metadata: {
            stage: 'concept-extraction',
            retries: this.currentRetry,
            errorType: error instanceof Error ? error.constructor.name : 'UnknownError',
            errorMessage
          }
        };
      }
    });
  }

  /**
   * Prepare code context for LLM analysis
   */
  private prepareCodeContext(files: [string, string][]): string {
    const maxContextLength = 15000; // Limit context size
    let context = '';
    
    for (const [path, content] of files) {
      const fileSection = `\n\n=== ${path} ===\n${content}`;
      
      if (context.length + fileSection.length > maxContextLength) {
        context += '\n\n... (additional files truncated for brevity)';
        break;
      }
      
      context += fileSection;
    }
    
    return context;
  }

  /**
   * Create file listing for prompt
   */
  private createFileListing(files: [string, string][]): string {
    return files
      .map(([path], index) => `${index} # ${path}`)
      .join('\n');
  }

  /**
   * Parse concepts from LLM YAML response
   */
  private parseConceptsFromResponse(response: string): LearningConcept[] {
    try {
      // Extract YAML from response
      const yamlMatch = response.match(/```yaml\s*([\s\S]*?)\s*```/);
      if (!yamlMatch) {
        console.error('No YAML block found in response. Full response:', response);
        throw new Error('No YAML block found in response');
      }

      const yamlContent = yamlMatch[1];
      console.log('Extracted YAML content:', yamlContent);

      const parsed = yaml.load(yamlContent) as any;
      console.log('Parsed YAML object:', parsed);

      if (!parsed.concepts || !Array.isArray(parsed.concepts)) {
        console.error('Invalid concepts structure. Parsed object:', parsed);
        throw new Error('Invalid concepts structure in YAML');
      }

      const concepts = parsed.concepts.map((concept: any, index: number) => ({
        name: concept.name || `Concept ${index + 1}`,
        description: concept.description || 'No description provided',
        difficulty: concept.difficulty || 'beginner',
        prerequisites: concept.prerequisites || [],
        files: concept.file_indices || [],
        examples: concept.examples || []
      }));

      console.log('Successfully parsed concepts:', concepts);
      return concepts;

    } catch (error) {
      console.error('Error parsing concepts from response:', error);
      console.error('Response that failed to parse:', response);
      throw new Error(`Failed to parse concepts: ${error.message}`);
    }
  }

  /**
   * Validate and enhance extracted concepts
   */
  private validateAndEnhanceConcepts(concepts: LearningConcept[], files: [string, string][]): LearningConcept[] {
    return concepts.map(concept => {
      // Validate file indices
      const validFileIndices = concept.files.filter(index => 
        index >= 0 && index < files.length
      );

      // Ensure at least one example
      const examples = concept.examples.length > 0 
        ? concept.examples 
        : ['Basic usage example'];

      return {
        ...concept,
        files: validFileIndices,
        examples,
        // Ensure valid difficulty level
        difficulty: ['beginner', 'intermediate', 'advanced'].includes(concept.difficulty) 
          ? concept.difficulty as any
          : 'beginner'
      };
    });
  }

  /**
   * Get language instruction based on content language
   */
  private getLanguageInstruction(language: string): string {
    if (language && language.toLowerCase() !== 'english') {
      return `Write all content in ${language}. `;
    }
    return '';
  }

  /**
   * Get name language hint
   */
  private getNameLanguageHint(language: string): string {
    if (language && language.toLowerCase() !== 'english') {
      return ` (in ${language})`;
    }
    return '';
  }

  /**
   * Get description language hint
   */
  private getDescriptionLanguageHint(language: string): string {
    if (language && language.toLowerCase() !== 'english') {
      return ` (in ${language})`;
    }
    return '';
  }
}
