# Code2Documentation PangeaFlow Implementation

This directory contains the PangeaFlow implementation of the Code2Documentation system, providing enhanced error handling, progress reporting, and workflow orchestration compared to the original PocketFlow implementation.

## Overview

The PangeaFlow implementation converts the original PocketFlow nodes into PangeaFlow agents with improved:

- **Error Handling**: Comprehensive error handling with retry logic and dedicated error agent
- **Progress Reporting**: Detailed progress and status events for better user experience
- **Event System**: Event-driven architecture for real-time updates
- **Telemetry**: Built-in telemetry collection for performance monitoring
- **Modularity**: Agent-based architecture for better maintainability

## Architecture

### Agents

1. **FetchRepoAgent** - Fetches repository files from GitHub or local directory
2. **IdentifyAbstractionsAgent** - Identifies core abstractions in the codebase
3. **AnalyzeRelationshipsAgent** - Analyzes relationships between abstractions
4. **OrderChaptersAgent** - Determines optimal tutorial structure
5. **WriteChaptersAgent** - Generates individual tutorial chapters
6. **CombineTutorialAgent** - Combines chapters into final tutorial
7. **ErrorHandlerAgent** - <PERSON>les workflow errors

### Workflow Flow

```
Start → FetchRepo → IdentifyAbstractions → AnalyzeRelationships → OrderChapters → WriteChapters → CombineTutorial → Complete
                                                                                                                    ↓
                                                                                                                  Error → ErrorHandler
```

## Usage

### Basic Usage

```typescript
import { executeCode2DocumentationWorkflow, createDefaultSharedStore } from './pangeaflow';

// Create shared store with your parameters
const shared = createDefaultSharedStore({
  user_id: 'your-user-id',
  repo_url: 'https://github.com/your/repo',
  selected_files: ['src/index.js', 'src/components/App.js'],
  language: 'english',
  max_abstraction_num: 10
});

// Execute workflow
const result = await executeCode2DocumentationWorkflow(shared);

if (result.success) {
  console.log('Tutorial generated:', result.data.tutorial);
} else {
  console.error('Error:', result.error);
}
```

### Event Handling

```typescript
import { onProgress, onStatus, onError, onComplete } from './utils/events';

// Subscribe to progress updates
onProgress((event) => {
  console.log(`${event.component}: ${event.progress}% - ${event.message}`);
});

// Subscribe to status updates
onStatus((event) => {
  console.log(`${event.component}: ${event.message}`);
});

// Subscribe to errors
onError((event) => {
  console.error(`Error in ${event.component}: ${event.message}`);
});

// Subscribe to completion
onComplete((event) => {
  console.log('Workflow completed:', event.result);
});
```

## Configuration

### Shared Store Parameters

- `user_id` (required): User identifier
- `repo_url` or `local_dir` (required): Repository URL or local directory path
- `selected_files` (required): Array of file paths to process
- `language` (optional): Output language (default: 'english')
- `use_cache` (optional): Whether to use LLM caching (default: true)
- `max_abstraction_num` (optional): Maximum number of abstractions (default: 10)
- `github_token` (optional): GitHub token for private repositories
- `project_name` (optional): Project name (auto-detected if not provided)

### Validation

```typescript
import { validateSharedStore } from './pangeaflow';

const validation = validateSharedStore(shared);
if (!validation.valid) {
  console.error('Validation errors:', validation.errors);
}
```

## Comparison with PocketFlow

| Feature | PocketFlow | PangeaFlow |
|---------|------------|------------|
| Error Handling | Basic | Comprehensive with retry logic |
| Progress Reporting | Limited | Detailed with real-time updates |
| Event System | Simple | Event-driven architecture |
| Telemetry | None | Built-in performance monitoring |
| State Management | Shared store mutation | Immutable context passing |
| Modularity | Node-based | Agent-based |

## Migration from PocketFlow

To migrate from PocketFlow to PangeaFlow:

1. **Update imports**:
   ```typescript
   // Old
   import { create_tutorial_flow } from '../flow/flow';
   
   // New
   import { executeCode2DocumentationWorkflow } from '../pangeaflow';
   ```

2. **Update function calls**:
   ```typescript
   // Old
   const flow = create_tutorial_flow();
   await flow.run(shared);
   
   // New
   const result = await executeCode2DocumentationWorkflow(shared);
   ```

3. **Handle results**:
   ```typescript
   if (result.success) {
     // Process successful result
     const tutorial = result.data.tutorial;
   } else {
     // Handle error
     console.error("Error:", result.error);
   }
   ```

## Testing

Run the test suite:

```bash
npm test -- --testPathPattern=pangeaFlow
```

## Troubleshooting

### Common Issues

1. **Import errors**: Ensure all PangeaFlow dependencies are properly installed
2. **Event not firing**: Check that event listeners are set up before workflow execution
3. **Validation errors**: Use `validateSharedStore()` to check parameter validity

### Debug Mode

Enable debug logging:

```typescript
// Set environment variable
process.env.DEBUG = 'pangeaflow:*';
```

## Contributing

When adding new agents or modifying existing ones:

1. Follow the established agent pattern
2. Implement proper error handling with retry logic
3. Emit appropriate progress and status events
4. Add comprehensive tests
5. Update documentation

## License

Same as the main project license.
